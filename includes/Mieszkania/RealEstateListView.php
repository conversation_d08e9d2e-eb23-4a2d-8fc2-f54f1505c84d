<?php

namespace DD\App\Mieszkania;

use DD\App\DTO\RealEstate;
use DD\App\Enum\SortDirection;
use DD\App\Enum\SortField;
use DD\App\Enum\Type;
use DD\App\Repository\RealEstateRepository;

class RealEstateListView
{
    public static function render(): void
    {
        $realEstateRepository = new RealEstateRepository();

        $realEstates = $realEstateRepository->getAllRealEstates();

        self::renderContent($realEstates);
    }

    /**
     * @param array<RealEstate> $realEstates
     */
    private static function renderContent(array $realEstates): void
    {
        if (empty($realEstates)) {
            echo '<p>Brak dostępnych lokali.</p>';
            return;
        }

        echo '<div class="real-estate-table-container">';
        echo '<table class="real-estate-table">';
        echo '<thead>';
        echo '<tr>';
        echo '<th>Lokal</th>';
        echo '<th>Powierzchnia</th>';
        echo '<th>Cena brutto</th>';
        echo '<th>Status</th>';
        echo '<th>Szczegóły</th>';
        echo '</tr>';
        echo '</thead>';
        echo '<tbody>';

        foreach ($realEstates as $realEstate) {
            echo '<tr>';

            // Lokal
            echo '<td>' . esc_html($realEstate->type->displayName()) . ' ' . esc_html($realEstate->localNumber) . '</td>';

            // Powierzchnia
            if ($realEstate->area > 0) {
                echo '<td>' . number_format($realEstate->area, 2, ',', ' ') . ' m² ' . '</td>';
            } else {
                echo '<td>-</td>';
            }

            // Cena brutto z ceną za metr
            echo '<td>';
            if ($realEstate->price > 0) {
                echo '<div class="price-container">';
                echo '<div class="main-price">' . number_format($realEstate->price, 0, ',', ' ') . ' zł</div>';
                if ($realEstate->pricePerMeter > 0) {
                    echo '<div class="price-per-meter">' . number_format($realEstate->pricePerMeter, 0, ',', ' ') . ' zł / m²</div>';
                }
                echo '</div>';
            } else {
                echo '-';
            }
            echo '</td>';

            // Status
            echo '<td>';
            echo '<span class="status ' . esc_attr($realEstate->status->value) . '">';
            echo esc_html($realEstate->status->displayName());
            echo '</span>';
            echo '</td>';

            // Link do szczegółów
            echo '<td>';
            if ($realEstate->postId) {
                $permalink = get_permalink($realEstate->postId);
                if ($permalink) {
                    echo '<a href="' . esc_url($permalink) . '" class="details-link">Zobacz szczegóły</a>';
                } else {
                    echo '-';
                }
            } else {
                echo '-';
            }
            echo '</td>';

            echo '</tr>';
        }

        echo '</tbody>';
        echo '</table>';
        echo '</div>';
    }
}